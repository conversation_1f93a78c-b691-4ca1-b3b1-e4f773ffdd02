import React from "react";
import { ContextMenuContent, ContextMenuItem, ContextMenuSub, ContextMenuSubTrigger, ContextMenuSubContent } from "@/components/ui/context-menu";
import { Play, Copy, Crop, Scissors, Combine, Download, X, Layers, ChevronUp, ChevronDown, MoveUp, MoveDown, LoaderCircleIcon } from "lucide-react";
import type { PlacedImage, GenerationSettings } from "@/@types/canvas";

interface CanvasContextMenuProps {
	selectedIds: string[];
	images: PlacedImage[];
	isGenerating: boolean;
	generationSettings: GenerationSettings;
	handleRun: () => void;
	handleDuplicate: () => void;
	handleRemoveBackground: () => void;
	handleCombineImages: () => void;
	handleDelete: () => void;
	setCroppingImageId: (id: string | null) => void;
	sendToFront: () => void;
	sendToBack: () => void;
	bringForward: () => void;
	sendBackward: () => void;
}

export const CanvasContextMenu: React.FC<CanvasContextMenuProps> = ({
	selectedIds,
	images,
	isGenerating,
	generationSettings,
	handleRun,
	handleDuplicate,
	handleRemoveBackground,
	handleCombineImages,
	handleDelete,
	setCroppingImageId,
	sendToFront,
	sendToBack,
	bringForward,
	sendBackward,
}) => {
	return (
		<ContextMenuContent>
			<ContextMenuItem
				onClick={handleRun}
				disabled={isGenerating || !generationSettings.prompt.trim()}
				className="flex items-center justify-between gap-2"
			>
				<div className="flex items-center gap-2">
					{isGenerating ? <LoaderCircleIcon className="text-content h-4 w-4 animate-spin" /> : <Play className="text-content h-4 w-4" />}
					<span>Run</span>
				</div>
			</ContextMenuItem>
			<ContextMenuItem onClick={handleDuplicate} disabled={selectedIds.length === 0} className="flex items-center gap-2">
				<Copy className="h-4 w-4" />
				Duplicate
			</ContextMenuItem>
			<ContextMenuItem
				onClick={() => {
					if (selectedIds.length === 1) {
						setCroppingImageId(selectedIds[0]);
					}
				}}
				disabled={selectedIds.length !== 1}
				className="flex items-center gap-2"
			>
				<Crop className="h-4 w-4" />
				Crop
			</ContextMenuItem>
			<ContextMenuItem onClick={handleRemoveBackground} disabled={selectedIds.length === 0} className="flex items-center gap-2">
				<Scissors className="h-4 w-4" />
				Remove Background
			</ContextMenuItem>
			<ContextMenuItem onClick={handleCombineImages} disabled={selectedIds.length < 2} className="flex items-center gap-2">
				<Combine className="h-4 w-4" />
				Combine Images
			</ContextMenuItem>
			<ContextMenuSub>
				<ContextMenuSubTrigger disabled={selectedIds.length === 0} className="flex items-center gap-2">
					<Layers className="h-4 w-4" />
					Layer Order
				</ContextMenuSubTrigger>
				<ContextMenuSubContent className="w-64" sideOffset={10}>
					<ContextMenuItem onClick={sendToFront} disabled={selectedIds.length === 0} className="flex items-center justify-between gap-2">
						<div className="flex items-center gap-2">
							<MoveUp className="h-4 w-4" />
							<span>Send to Front</span>
						</div>
					</ContextMenuItem>
					<ContextMenuItem onClick={bringForward} disabled={selectedIds.length === 0} className="flex items-center justify-between gap-2">
						<div className="flex items-center gap-2">
							<ChevronUp className="h-4 w-4" />
							<span>Bring Forward</span>
						</div>
					</ContextMenuItem>
					<ContextMenuItem onClick={sendBackward} disabled={selectedIds.length === 0} className="flex items-center justify-between gap-2">
						<div className="flex items-center gap-2">
							<ChevronDown className="h-4 w-4" />
							<span>Send Backward</span>
						</div>
					</ContextMenuItem>
					<ContextMenuItem onClick={sendToBack} disabled={selectedIds.length === 0} className="flex items-center justify-between gap-2">
						<div className="flex items-center gap-2">
							<MoveDown className="h-4 w-4" />
							<span>Send to Back</span>
						</div>
					</ContextMenuItem>
				</ContextMenuSubContent>
			</ContextMenuSub>
			<ContextMenuItem
				onClick={async () => {
					for (const id of selectedIds) {
						const image = images.find((img) => img.id === id);

						if (image) {
							const link = document.createElement("a");
							link.download = `image-${Date.now()}.png`;
							link.href = image.src;
							link.click();
						}
					}
				}}
				disabled={selectedIds.length === 0}
				className="flex items-center gap-2"
			>
				<Download className="h-4 w-4" />
				Download
			</ContextMenuItem>
			<ContextMenuItem onClick={handleDelete} disabled={selectedIds.length === 0} className="text-destructive flex items-center gap-2">
				<X className="h-4 w-4" />
				Delete
			</ContextMenuItem>
		</ContextMenuContent>
	);
};
