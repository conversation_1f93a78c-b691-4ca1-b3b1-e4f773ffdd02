import React, { useEffect } from "react";
import { useMutation } from "@tanstack/react-query";
import { orpc } from "@/orpc/client";
import type { ActiveGeneration } from "@/@types/canvas";

interface HandleGenerateImageProps {
	imageId: string;
	generation: ActiveGeneration;
	onComplete: (imageId: string, finalUrl: string) => void;
	onError: (imageId: string, error: string) => void;
	apiKey?: string;
}

export const HandleGenerateImage: React.FC<HandleGenerateImageProps> = ({ imageId, generation, onComplete, onError, apiKey }) => {
	const { mutateAsync: editImage } = useMutation(orpc.image.editImage.mutationOptions());

	useEffect(() => {
		const generateImage = async () => {
			try {
				console.log("Starting image generation with orpc.image.editImage", {
					imageId,
					prompt: generation.prompt,
					imageUrl: generation.imageUrl,
				});

				const result = await editImage({
					prompt: generation.prompt,
					images: [generation.imageUrl],
				});

				console.log("Image generation completed", { imageId, result });

				// Get the first result URL
				if (result.resultUrls && result.resultUrls.length > 0) {
					onComplete(imageId, result.resultUrls[0]);
				} else {
					onError(imageId, "No result URLs returned from generation");
				}
			} catch (error) {
				console.error("Image generation error:", error);
				onError(imageId, error instanceof Error ? error.message : "Generation failed");
			}
		};

		generateImage();
	}, [imageId, generation.prompt, generation.imageUrl, editImage, onComplete, onError]);

	return null;
};
