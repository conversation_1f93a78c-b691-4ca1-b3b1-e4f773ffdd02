import React from "react";
import { Button } from "@/components/ui/button";
import { PlusIcon, MinusIcon } from "lucide-react";
import { cn } from "@/lib/utils";

interface ZoomControlsProps {
	viewport: {
		x: number;
		y: number;
		scale: number;
	};
	setViewport: (viewport: { x: number; y: number; scale: number }) => void;
	canvasSize: {
		width: number;
		height: number;
	};
}

export const ZoomControls: React.FC<ZoomControlsProps> = ({ viewport, setViewport, canvasSize }) => {
	const handleZoomIn = () => {
		const newScale = Math.min(5, viewport.scale * 1.2);
		const centerX = canvasSize.width / 2;
		const centerY = canvasSize.height / 2;

		// Zoom towards center
		const mousePointTo = {
			x: (centerX - viewport.x) / viewport.scale,
			y: (centerY - viewport.y) / viewport.scale,
		};

		setViewport({
			x: centerX - mousePointTo.x * newScale,
			y: centerY - mousePointTo.y * newScale,
			scale: newScale,
		});
	};

	const handleZoomOut = () => {
		const newScale = Math.max(0.1, viewport.scale / 1.2);
		const centerX = canvasSize.width / 2;
		const centerY = canvasSize.height / 2;

		// Zoom towards center
		const mousePointTo = {
			x: (centerX - viewport.x) / viewport.scale,
			y: (centerY - viewport.y) / viewport.scale,
		};

		setViewport({
			x: centerX - mousePointTo.x * newScale,
			y: centerY - mousePointTo.y * newScale,
			scale: newScale,
		});
	};

	const handleResetView = () => {
		setViewport({ x: 0, y: 0, scale: 1 });
	};

	return (
		<div className="absolute bottom-4 left-4 z-20 hidden flex-col items-start gap-4 md:flex">
			<div className={cn("bg-card flex flex-row items-center gap-1 overflow-clip rounded-full px-2 py-1")}>
				<Button variant="ghost" size="icon" onClick={handleZoomIn} className="size-6 rounded-full p-0">
					<PlusIcon className="size-3.5" />
				</Button>
				<p className="text-center text-[11px]">{Math.round(viewport.scale * 100)}%</p>
				<Button variant="ghost" size="sm" onClick={handleZoomOut} className="size-6 rounded-full p-0">
					<MinusIcon className="size-3.5" />
				</Button>
				{/* <Button variant="ghost" size="sm" onClick={handleResetView} className="h-10 w-10 rounded-none p-0" title="Reset view">
					<Maximize2 className="h-4 w-4" />
				</Button> */}
			</div>
		</div>
	);
};
