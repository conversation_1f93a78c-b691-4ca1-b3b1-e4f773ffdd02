import { handleError } from "@/@types/error";
import { ofetch } from "ofetch";
import { getFileExtension } from "./utils-file";

export const getUploadS3UrlAdmin = async (
	file: File,
	options: {
		prefixType: string;
		subPath?: string;
	},
): Promise<{ uploadUrl: string; method: string; file_url: string }> => {
	const fileExtension = getFileExtension(file.name);
	const { status, message, url, method, file_url } = await ofetch("/api/admin/upload-file", {
		method: "POST",
		body: { fileExtension: fileExtension, contentType: file.type, options },
	});
	handleError(status, message);

	return { uploadUrl: url, method, file_url };
};
export const uploadFileAdmin = async (
	file: File,
	options: {
		prefixType: string;
		subPath?: string;
	},
): Promise<{ file_url: string }> => {
	const { uploadUrl, method, file_url } = await getUploadS3UrlAdmin(file, options);

	// begin upload
	await ofetch(uploadUrl, {
		method: method,
		headers: {
			accept: "application/json",
		},
		body: file,
	}).catch((error) => {
		throw new Error(error.data.message || "Failed to upload file.");
	});

	return { file_url };
};

export const getUploadS3Url = async (file: File, temp: boolean = false): Promise<{ uploadUrl: string; method: string; file_url: string }> => {
	// 获取文件扩展名
	const fileExtension = getFileExtension(file.name);
	let endpoint = "/api/v1/upload-file";
	if (temp) {
		endpoint = "/api/v1/upload-file-temp";
	}
	const { status, message, url, method, file_url } = await ofetch("endpoint", {
		method: "POST",
		body: { fileExtension: fileExtension, contentType: file.type },
	});
	handleError(status, message);

	return { uploadUrl: url, method, file_url };
};

export const uploadFile = async (file: File, temp: boolean = false): Promise<{ file_url: string }> => {
	const { uploadUrl, method, file_url } = await getUploadS3Url(file, temp);

	// begin upload
	await ofetch(uploadUrl, {
		method: method,
		headers: {
			accept: "application/json",
		},
		body: file,
	}).catch((error) => {
		throw new Error(error.data.message || "Failed to upload file.");
	});

	return { file_url };
};

interface UploadProps {
	file: File;
	uploadUrl: string;
	method: string;
	onProgress?: (event: { progress: number }) => void;
}
export const handleFileUploadWithProgress = async ({ file, uploadUrl, method, onProgress }: UploadProps) => {
	try {
		// const fileFormData = new FormData();
		// fileFormData.append("file", file);

		return await new Promise<void>((resolve, reject) => {
			const xhr = new XMLHttpRequest();

			xhr.upload.onprogress = (event) => {
				if (event.lengthComputable) {
					const loaded = event.loaded;
					const total = event.total;
					const percentage = Math.round((loaded / total) * 100);
					onProgress?.({ progress: percentage });
				}
			};

			xhr.onload = () => {
				if (xhr.status >= 200 && xhr.status < 300) {
					console.log("Upload file success!");
					resolve(); // Resolve the promise on successful upload
				} else {
					reject(new Error(`Failed to upload file. Status: ${xhr.status}`)); // Reject on HTTP error
				}
			};

			xhr.onerror = () => {
				reject(new Error("Failed to upload file. Network error.")); // Reject on network error
			};

			xhr.open(method, uploadUrl);
			// xhr.send(fileFormData);

			// Set the request header to indicate binary data
			// xhr.setRequestHeader("Content-Type", "application/octet-stream");

			const reader = new FileReader();
			reader.onload = (event) => {
				if (event.target && event.target.result) {
					// Send the file content as binary data
					xhr.send(event.target.result);
				} else {
					reject(new Error("Failed to read file"));
				}
			};
			reader.onerror = () => {
				reject(new Error("Failed to read file"));
			};
			reader.readAsArrayBuffer(file);
		});
	} catch (error) {
		console.error("Error during file upload:", error);
		throw error;
	}
};
